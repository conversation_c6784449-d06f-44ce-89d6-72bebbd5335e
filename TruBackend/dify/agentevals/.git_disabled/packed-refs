# pack-refs with: peeled fully-peeled sorted 
16bca93cb66c5a72d04fb636309bf024d07ce9d3 refs/remotes/origin/isaac/addingci
fa1bbafec836fb8f33224f6598817b681cb6fbcd refs/remotes/origin/isaac/jsreadmeupdate
e006c77ad50719e42ab34e51c8ce4c6534f9f33b refs/remotes/origin/isaac/prompthubsupport
fceca1bd1f3ecd1f4b275eb1af252ccc5d43bef9 refs/remotes/origin/isaac/promptstuff
4df347f73499e84b6b6b312b4408edc9bf98e786 refs/remotes/origin/jacob/ci
a7c3df69d315d317c5269437d566e28c9bd2e5c0 refs/remotes/origin/jacob/deps
7f88a02f6b1dc83b7b620ca551546827638df143 refs/remotes/origin/jacob/docs
9eba847f5f1758df146e64e7fcbf99c942bcaa18 refs/remotes/origin/jacob/hotfix
99c965391441db5449ed569e6474b72367362c18 refs/remotes/origin/jacob/nits
a55552a3739d5c5ffa665ec409858abac2a34715 refs/remotes/origin/jacob/packages
749db1b72073e0964a3966468f58be51fea603d9 refs/remotes/origin/jacob/readme
19eb2464a369380f721241d0d790e356e1e84bb0 refs/remotes/origin/jacob/readmes
af47a1865c5cccf28ca5f9b402948e51051f132a refs/remotes/origin/jacob/release
a50874b1b3ff07b98b75828107850e8edf03b389 refs/remotes/origin/jacob/tests
3be45be14fd4002231b9bd79072387a0a42d6880 refs/remotes/origin/jacob/tools
1ce759283ce0285e83c7345a2c19ffd9e635424b refs/remotes/origin/jacob/trajectory_formatting
0126a58f6ef4d6d2a802bb19eec0877d31ba4f1b refs/remotes/origin/jacob/trajectory_rework
476c6791ba10d76805b568a4a2ac541c423c6a30 refs/remotes/origin/jacob/versions
64b5bb160a0801f3e1eaf133b9c18774a71a5c2f refs/remotes/origin/jacob/wrappers
9cbed10516d637eb6fc25acd26e8bfdaf62f895a refs/remotes/origin/main
5fe759fd1396843d320ddea02e781cbba93f0a3f refs/remotes/origin/release
fb13f6638900d62cd23d0ced91d636a100d99f92 refs/tags/0.0.2
70a7a2e1230c7c1153e762369cbd20c51a26e0e2 refs/tags/0.0.3
c82e540d8e0359652ba42caad0c8f7eeeeafa528 refs/tags/js==0.0.4
01a39d420fc5e06fb100ab72a588e72e7588fff0 refs/tags/js==0.0.5
4dc52b3318229ef19ae7f0b98bb1145198fc27e4 refs/tags/py==0.0.4
f48346f5eefc2a6ea28a9b6a820fb9038dd2926d refs/tags/py==0.0.5
c5be3d6ed68c8b7651a5d7342faf8bf3d38c9e2f refs/tags/py==0.0.6
9cbed10516d637eb6fc25acd26e8bfdaf62f895a refs/tags/py==0.0.7
